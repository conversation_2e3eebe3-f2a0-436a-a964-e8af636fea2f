import requests
import pandas as pd
from datetime import datetime

# API configuration
API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************.5iBW40ktLyibY4V7DxuxnEmXBCaqRZfuDhaHxqtGqpk"  # Your API key
BASE_URL = "https://api.theirstack.com/v1/companies/search"
HEADERS = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json",
    "Accept": "application/json",
    "X-Use-Clickhouse": "true"
}

# Query payload
payload = {
    "page": 0,
    "limit": 200,  # Set to 200 as requested
    "order_by": [
        {"desc": True, "field": "confidence"},
        {"desc": True, "field": "jobs"}
    ],
    "include_total_results": False,
    "blur_company_data": False,
    "company_technology_slug_or": ["netsuite"],
    "company_country_code_or": ["US"],
    "industry_or": ["manufacturing"],
    "expand_technology_slugs": []
}

# Make the API request
try:
    response = requests.post(BASE_URL, headers=HEADERS, json=payload)
    response.raise_for_status()  # Check for HTTP errors

    # Parse JSON response
    data = response.json()

    # Extract company data
    companies = data.get("data", [])
    if not companies:
        print("No companies found with the specified filters.")
    else:
        # Prepare data for Excel
        output_data = []
        for company in companies:
            output_data.append({
                "Company": company.get("name", "N/A"),
                "Domain": company.get("domain", "N/A"),
                "Technologies": ", ".join(company.get("technology_names", ["N/A"])),
                "Confidence Score": company.get("confidence_score", "N/A"),
                "Company Size": company.get("employee_count", "N/A"),
                "Revenue Range": company.get("revenue_range", "N/A"),
                "Technology Adoption Date": company.get("first_seen_date", "N/A"),
                "Industry Subcategory": company.get("industry_subcategory", "N/A")
            })

        # Create DataFrame
        df = pd.DataFrame(output_data)

        # Generate timestamped filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"netsuite_manufacturing_us_{timestamp}.xlsx"

        # Save to Excel
        df.to_excel(filename, index=False, engine="openpyxl")
        print(f"Data saved to {filename}")

        # Print results for verification
        for company in output_data:
            print(f"Company: {company['Company']}")
            print(f"Domain: {company['Domain']}")
            print(f"Technologies: {company['Technologies']}")
            print(f"Confidence Score: {company['Confidence Score']}")
            print(f"Company Size: {company['Company Size']}")
            print(f"Revenue Range: {company['Revenue Range']}")
            print(f"Technology Adoption Date: {company['Technology Adoption Date']}")
            print(f"Industry Subcategory: {company['Industry Subcategory']}")
            print("-" * 50)

        # Check remaining credits
        remaining_credits = response.headers.get("X-Rate-Limit-Remaining", "Unknown")
        print(f"Remaining API Credits: {remaining_credits}")

except requests.exceptions.HTTPError as err:
    print(f"HTTP Error: {err}")
    if response.status_code == 422:
        try:
            error_details = response.json()
            print("Error Details:", error_details)
        except ValueError:
            print("No detailed error message provided by the API.")
except requests.exceptions.RequestException as err:
    print(f"Error: {err}")
except Exception as e:
    print(f"Unexpected Error: {e}")